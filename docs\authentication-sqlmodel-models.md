# Mô Hình SQLModel cho Hệ Thống Xác Thực SkinAid

## Tổng Quan SQLModel

SQLModel là thư viện kết hợp sức mạnh của SQLAlchemy (ORM) và Pydantic (validation & serialization), được tạo bởi tác giả FastAPI. Nó cho phép định nghĩa models một lần và sử dụng cho cả database ORM và API serialization.

### Ưu Điểm SQLModel
- **Một Model, Nhiều Mục Đích**: Sử dụng cho cả database và API
- **Type Safety**: Hỗ trợ type hints đầy đủ
- **Validation**: Tự động validate dữ liệu với Pydantic
- **FastAPI Integration**: Tích hợp hoàn hảo với FastAPI
- **SQLAlchemy Power**: Sử dụng tất cả tính năng của SQLAlchemy

## C<PERSON><PERSON> Hình Cơ Bản và Import

```python
"""
Mô hình SQLModel cho hệ thống xác thực SkinAid.
Các model này kết hợp SQLAlchemy ORM và Pydantic validation,
phù hợp cho FastAPI + PostgreSQL.
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict, Any, Union
from uuid import UUID, uuid4
import re

from sqlmodel import (
    SQLModel, Field, Relationship, Session, create_engine,
    Column, String, Text, Boolean, Integer, DateTime, JSON,
    Index, UniqueConstraint, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, INET, JSONB
from sqlalchemy.sql import func
from pydantic import validator, EmailStr, constr

# Cấu hình cơ sở dữ liệu
DATABASE_URL = "postgresql://user:password@localhost/skinaid_db"
engine = create_engine(DATABASE_URL, echo=True)

def create_db_and_tables():
    """Tạo tất cả bảng trong database."""
    SQLModel.metadata.create_all(engine)

def get_session():
    """Tạo database session."""
    with Session(engine) as session:
        yield session

# Hàm tạo UUID
def generate_uuid() -> str:
    """Tạo UUID string mới."""
    return str(uuid4())
```

## 1. User Model - Mô Hình Người Dùng

```python
class UserBase(SQLModel):
    """
    Base model cho User chứa các trường chung.
    Được sử dụng để tạo các model khác nhau cho API.
    """
    username: str = Field(
        min_length=3, 
        max_length=50,
        regex=r'^[A-Za-z0-9_]{3,50}$',
        description="Tên đăng nhập duy nhất (3-50 ký tự, chỉ chữ, số, gạch dưới)"
    )
    email: EmailStr = Field(
        description="Địa chỉ email hợp lệ"
    )
    first_name: Optional[str] = Field(
        default=None, 
        max_length=100,
        description="Tên"
    )
    last_name: Optional[str] = Field(
        default=None, 
        max_length=100,
        description="Họ"
    )
    phone_number: Optional[str] = Field(
        default=None, 
        max_length=20,
        regex=r'^[\+]?[0-9\-\(\)\s]{10,20}$',
        description="Số điện thoại hợp lệ"
    )
    is_active: bool = Field(
        default=True,
        description="Tài khoản có hoạt động không"
    )
    is_verified: bool = Field(
        default=False,
        description="Email đã được xác thực chưa"
    )
    account_status: str = Field(
        default="pending",
        regex=r'^(pending|active|suspended|deactivated)$',
        description="Trạng thái tài khoản"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Thông tin bổ sung (dữ liệu y tế, preferences)"
    )

class User(UserBase, table=True):
    """
    Model User chính cho database.
    Chứa tất cả thông tin người dùng và xác thực.
    """
    __tablename__ = "users"
    
    # Primary key
    id: Optional[str] = Field(
        default_factory=generate_uuid,
        primary_key=True,
        sa_column=Column(PostgresUUID(as_uuid=False)),
        description="Định danh duy nhất của người dùng"
    )
    
    # Authentication fields
    password_hash: str = Field(
        description="Mật khẩu đã mã hóa bcrypt (không bao giờ lưu plain text)"
    )
    
    # Additional security fields
    is_superuser: bool = Field(
        default=False,
        description="Có quyền quản trị viên không"
    )
    last_login: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True)),
        description="Lần đăng nhập cuối cùng"
    )
    failed_login_attempts: int = Field(
        default=0,
        description="Số lần đăng nhập thất bại liên tiếp"
    )
    locked_until: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True)),
        description="Tài khoản bị khóa đến khi nào"
    )
    
    # Audit timestamps
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True), server_default=func.current_timestamp()),
        description="Thời gian tạo tài khoản"
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(
            DateTime(timezone=True), 
            server_default=func.current_timestamp(),
            onupdate=func.current_timestamp()
        ),
        description="Thời gian cập nhật cuối"
    )
    
    # Relationships
    user_roles: List["UserRole"] = Relationship(
        back_populates="user",
        cascade_delete=True
    )
    sessions: List["Session"] = Relationship(
        back_populates="user",
        cascade_delete=True
    )
    password_reset_tokens: List["PasswordResetToken"] = Relationship(
        back_populates="user",
        cascade_delete=True
    )
    email_verification_tokens: List["EmailVerificationToken"] = Relationship(
        back_populates="user",
        cascade_delete=True
    )
    
    # Table constraints
    __table_args__ = (
        Index("idx_users_email", "email"),
        Index("idx_users_username", "username"),
        Index("idx_users_active_verified", "is_active", "is_verified"),
        Index("idx_users_account_status", "account_status"),
        Index("idx_users_created_at", "created_at"),
        UniqueConstraint("email", name="uq_users_email"),
        UniqueConstraint("username", name="uq_users_username"),
        CheckConstraint(
            "account_status IN ('pending', 'active', 'suspended', 'deactivated')",
            name="chk_users_account_status"
        ),
    )
    
    @property
    def full_name(self) -> str:
        """Trả về họ tên đầy đủ."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name or self.last_name or self.username
    
    @property
    def is_locked(self) -> bool:
        """Kiểm tra tài khoản có bị khóa không."""
        if not self.locked_until:
            return False
        return datetime.now(timezone.utc) < self.locked_until
    
    def get_roles(self) -> List[str]:
        """Lấy danh sách tên vai trò của người dùng."""
        return [
            ur.role.name for ur in self.user_roles 
            if ur.is_active and ur.role.is_active
        ]

# API Models cho User
class UserCreate(UserBase):
    """Model để tạo user mới qua API."""
    password: str = Field(
        min_length=8,
        max_length=128,
        description="Mật khẩu (tối thiểu 8 ký tự)"
    )
    
    @validator('password')
    def validate_password(cls, v):
        """Validate mật khẩu mạnh."""
        if len(v) < 8:
            raise ValueError('Mật khẩu phải có ít nhất 8 ký tự')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Mật khẩu phải có ít nhất 1 chữ hoa')
        if not re.search(r'[a-z]', v):
            raise ValueError('Mật khẩu phải có ít nhất 1 chữ thường')
        if not re.search(r'\d', v):
            raise ValueError('Mật khẩu phải có ít nhất 1 chữ số')
        return v

class UserRead(UserBase):
    """Model để đọc thông tin user từ API."""
    id: str
    roles: List[str] = []
    created_at: datetime
    last_login: Optional[datetime] = None

class UserUpdate(SQLModel):
    """Model để cập nhật thông tin user."""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class UserLogin(SQLModel):
    """Model cho đăng nhập."""
    username_or_email: str = Field(description="Tên đăng nhập hoặc email")
    password: str = Field(description="Mật khẩu")
```

## 2. Role Model - Mô Hình Vai Trò

```python
class RoleBase(SQLModel):
    """Base model cho Role."""
    name: str = Field(
        max_length=50,
        regex=r'^[a-z_]+$',
        description="Tên vai trò (chỉ chữ thường và gạch dưới)"
    )
    display_name: str = Field(
        max_length=100,
        description="Tên hiển thị"
    )
    description: Optional[str] = Field(
        default=None,
        description="Mô tả chi tiết vai trò"
    )
    level: int = Field(
        default=0,
        ge=0,
        le=10,
        description="Cấp độ phân quyền (0-10, số càng cao quyền càng lớn)"
    )
    permissions: List[str] = Field(
        default_factory=list,
        description="Danh sách quyền hạn"
    )
    is_active: bool = Field(
        default=True,
        description="Vai trò có hoạt động không"
    )

class Role(RoleBase, table=True):
    """Model Role chính cho database."""
    __tablename__ = "roles"
    
    id: Optional[str] = Field(
        default_factory=generate_uuid,
        primary_key=True,
        sa_column=Column(PostgresUUID(as_uuid=False))
    )
    
    # Audit timestamps
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True), server_default=func.current_timestamp())
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(
            DateTime(timezone=True), 
            server_default=func.current_timestamp(),
            onupdate=func.current_timestamp()
        )
    )
    
    # Relationships
    user_roles: List["UserRole"] = Relationship(back_populates="role")
    
    # Table constraints
    __table_args__ = (
        Index("idx_roles_name", "name"),
        Index("idx_roles_active", "is_active"),
        Index("idx_roles_level", "level"),
        UniqueConstraint("name", name="uq_roles_name"),
    )
    
    def has_permission(self, permission: str) -> bool:
        """Kiểm tra vai trò có quyền cụ thể không."""
        return permission in self.permissions

# API Models cho Role
class RoleCreate(RoleBase):
    """Model tạo role mới."""
    pass

class RoleRead(RoleBase):
    """Model đọc thông tin role."""
    id: str
    created_at: datetime

class RoleUpdate(SQLModel):
    """Model cập nhật role."""
    display_name: Optional[str] = None
    description: Optional[str] = None
    permissions: Optional[List[str]] = None
    is_active: Optional[bool] = None
```

## 3. UserRole Model - Mô Hình Phân Quyền

```python
class UserRoleBase(SQLModel):
    """Base model cho UserRole."""
    is_active: bool = Field(
        default=True,
        description="Phân quyền có hoạt động không"
    )
    expires_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True)),
        description="Thời gian hết hạn phân quyền (tùy chọn)"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Thông tin bổ sung về phân quyền"
    )

class UserRole(UserRoleBase, table=True):
    """Model UserRole cho database - bảng liên kết User và Role."""
    __tablename__ = "user_roles"
    
    # Composite primary key
    user_id: str = Field(
        primary_key=True,
        foreign_key="users.id",
        sa_column=Column(PostgresUUID(as_uuid=False))
    )
    role_id: str = Field(
        primary_key=True,
        foreign_key="roles.id",
        sa_column=Column(PostgresUUID(as_uuid=False))
    )
    
    # Assignment metadata
    assigned_by: Optional[str] = Field(
        default=None,
        foreign_key="users.id",
        sa_column=Column(PostgresUUID(as_uuid=False)),
        description="Người phân quyền"
    )
    assigned_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True), server_default=func.current_timestamp()),
        description="Thời gian phân quyền"
    )
    
    # Relationships
    user: User = Relationship(back_populates="user_roles")
    role: Role = Relationship(back_populates="user_roles")
    assigner: Optional[User] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "UserRole.assigned_by"}
    )
    
    # Table constraints
    __table_args__ = (
        Index("idx_user_roles_user_id", "user_id"),
        Index("idx_user_roles_role_id", "role_id"),
        Index("idx_user_roles_active", "is_active"),
        Index("idx_user_roles_expires_at", "expires_at"),
    )
    
    @property
    def is_expired(self) -> bool:
        """Kiểm tra phân quyền có hết hạn không."""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at

# API Models cho UserRole
class UserRoleCreate(SQLModel):
    """Model tạo phân quyền mới."""
    user_id: str
    role_name: str
    expires_at: Optional[datetime] = None

class UserRoleRead(UserRoleBase):
    """Model đọc thông tin phân quyền."""
    user_id: str
    role_id: str
    role_name: str
    assigned_at: datetime
    assigned_by: Optional[str] = None
```

## 4. Session Model - Mô Hình Phiên Đăng Nhập

```python
class SessionBase(SQLModel):
    """Base model cho Session."""
    device_info: Dict[str, Any] = Field(
        default_factory=dict,
        description="Thông tin thiết bị (browser, OS, device type)"
    )
    ip_address: Optional[str] = Field(
        default=None,
        description="Địa chỉ IP client"
    )
    user_agent: Optional[str] = Field(
        default=None,
        description="Thông tin trình duyệt"
    )
    is_active: bool = Field(
        default=True,
        description="Phiên có hoạt động không"
    )

class Session(SessionBase, table=True):
    """Model Session cho database - quản lý phiên đăng nhập."""
    __tablename__ = "sessions"

    id: Optional[str] = Field(
        default_factory=generate_uuid,
        primary_key=True,
        sa_column=Column(PostgresUUID(as_uuid=False))
    )

    # User reference
    user_id: str = Field(
        foreign_key="users.id",
        sa_column=Column(PostgresUUID(as_uuid=False)),
        description="ID người dùng"
    )

    # Session tokens
    session_token: str = Field(
        unique=True,
        description="JWT token hoặc session identifier"
    )
    refresh_token: Optional[str] = Field(
        default=None,
        unique=True,
        description="Token để làm mới access token"
    )

    # Session timing
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True), server_default=func.current_timestamp())
    )
    expires_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True)),
        description="Thời gian hết hạn phiên"
    )
    last_accessed: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True), server_default=func.current_timestamp()),
        description="Lần truy cập cuối cùng"
    )

    # Revocation info
    revoked_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True)),
        description="Thời gian thu hồi phiên"
    )
    revoked_by: Optional[str] = Field(
        default=None,
        foreign_key="users.id",
        sa_column=Column(PostgresUUID(as_uuid=False)),
        description="Người thu hồi phiên"
    )
    revoke_reason: Optional[str] = Field(
        default=None,
        max_length=100,
        description="Lý do thu hồi phiên"
    )

    # Relationships
    user: User = Relationship(back_populates="sessions")
    revoker: Optional[User] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "Session.revoked_by"}
    )

    # Table constraints
    __table_args__ = (
        Index("idx_sessions_user_id", "user_id"),
        Index("idx_sessions_session_token", "session_token"),
        Index("idx_sessions_refresh_token", "refresh_token"),
        Index("idx_sessions_active", "is_active", "expires_at"),
        Index("idx_sessions_expires_at", "expires_at"),
    )

    @property
    def is_expired(self) -> bool:
        """Kiểm tra phiên có hết hạn không."""
        return datetime.now(timezone.utc) > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Kiểm tra phiên có hợp lệ không (active và chưa hết hạn)."""
        return self.is_active and not self.is_expired and not self.revoked_at

# API Models cho Session
class SessionCreate(SQLModel):
    """Model tạo session mới."""
    user_id: str
    expires_in_minutes: int = Field(default=30, ge=1, le=10080)  # Max 7 days
    device_info: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class SessionRead(SessionBase):
    """Model đọc thông tin session."""
    id: str
    user_id: str
    created_at: datetime
    expires_at: datetime
    last_accessed: datetime
    is_expired: bool
    is_valid: bool

class TokenResponse(SQLModel):
    """Model response cho authentication tokens."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
```

## 5. PasswordResetToken Model - Mô Hình Token Đặt Lại Mật Khẩu

```python
class PasswordResetTokenBase(SQLModel):
    """Base model cho PasswordResetToken."""
    ip_address: Optional[str] = Field(
        default=None,
        description="IP yêu cầu đặt lại mật khẩu"
    )
    user_agent: Optional[str] = Field(
        default=None,
        description="Trình duyệt yêu cầu đặt lại"
    )
    is_used: bool = Field(
        default=False,
        description="Token đã được sử dụng chưa"
    )
    is_revoked: bool = Field(
        default=False,
        description="Token đã bị thu hồi chưa"
    )

class PasswordResetToken(PasswordResetTokenBase, table=True):
    """Model PasswordResetToken cho database."""
    __tablename__ = "password_reset_tokens"

    id: Optional[str] = Field(
        default_factory=generate_uuid,
        primary_key=True,
        sa_column=Column(PostgresUUID(as_uuid=False))
    )

    # User reference
    user_id: str = Field(
        foreign_key="users.id",
        sa_column=Column(PostgresUUID(as_uuid=False)),
        description="ID người dùng"
    )

    # Token information
    token: str = Field(
        unique=True,
        description="Token bảo mật (gửi cho người dùng)"
    )
    token_hash: str = Field(
        description="Hash của token (lưu trong database)"
    )

    # Token timing
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True), server_default=func.current_timestamp())
    )
    expires_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True)),
        description="Thời gian hết hạn token (thường 1-24 giờ)"
    )
    used_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True)),
        description="Thời gian sử dụng token"
    )

    # Relationships
    user: User = Relationship(back_populates="password_reset_tokens")

    # Table constraints
    __table_args__ = (
        Index("idx_password_reset_tokens_user_id", "user_id"),
        Index("idx_password_reset_tokens_token_hash", "token_hash"),
        Index("idx_password_reset_tokens_expires_at", "expires_at"),
        CheckConstraint(
            "expires_at <= created_at + INTERVAL '24 hours'",
            name="chk_password_reset_token_expiry"
        ),
    )

    @property
    def is_expired(self) -> bool:
        """Kiểm tra token có hết hạn không."""
        return datetime.now(timezone.utc) > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Kiểm tra token có hợp lệ để sử dụng không."""
        return not self.is_used and not self.is_revoked and not self.is_expired

# API Models cho PasswordResetToken
class PasswordResetRequest(SQLModel):
    """Model yêu cầu đặt lại mật khẩu."""
    email: EmailStr = Field(description="Email của tài khoản cần đặt lại mật khẩu")

class PasswordResetConfirm(SQLModel):
    """Model xác nhận đặt lại mật khẩu."""
    token: str = Field(description="Token đặt lại mật khẩu")
    new_password: str = Field(
        min_length=8,
        max_length=128,
        description="Mật khẩu mới"
    )

    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate mật khẩu mới mạnh."""
        if len(v) < 8:
            raise ValueError('Mật khẩu phải có ít nhất 8 ký tự')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Mật khẩu phải có ít nhất 1 chữ hoa')
        if not re.search(r'[a-z]', v):
            raise ValueError('Mật khẩu phải có ít nhất 1 chữ thường')
        if not re.search(r'\d', v):
            raise ValueError('Mật khẩu phải có ít nhất 1 chữ số')
        return v
```

## 6. EmailVerificationToken Model - Mô Hình Token Xác Thực Email

```python
class EmailVerificationTokenBase(SQLModel):
    """Base model cho EmailVerificationToken."""
    email: EmailStr = Field(
        description="Email cần xác thực"
    )
    token_type: str = Field(
        default="registration",
        regex=r'^(registration|email_change|reactivation)$',
        description="Loại xác thực"
    )
    ip_address: Optional[str] = Field(
        default=None,
        description="IP yêu cầu xác thực"
    )
    user_agent: Optional[str] = Field(
        default=None,
        description="Trình duyệt yêu cầu"
    )
    is_used: bool = Field(
        default=False,
        description="Token đã sử dụng chưa"
    )
    is_revoked: bool = Field(
        default=False,
        description="Token đã thu hồi chưa"
    )

class EmailVerificationToken(EmailVerificationTokenBase, table=True):
    """Model EmailVerificationToken cho database."""
    __tablename__ = "email_verification_tokens"

    id: Optional[str] = Field(
        default_factory=generate_uuid,
        primary_key=True,
        sa_column=Column(PostgresUUID(as_uuid=False))
    )

    # User reference
    user_id: str = Field(
        foreign_key="users.id",
        sa_column=Column(PostgresUUID(as_uuid=False)),
        description="ID người dùng"
    )

    # Token information
    token: str = Field(
        unique=True,
        description="Token xác thực (gửi qua email)"
    )
    token_hash: str = Field(
        description="Hash của token (lưu trong database)"
    )

    # Token timing
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True), server_default=func.current_timestamp())
    )
    expires_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True)),
        description="Thời gian hết hạn token (24-72 giờ)"
    )
    verified_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True)),
        description="Thời gian xác thực thành công"
    )

    # Relationships
    user: User = Relationship(back_populates="email_verification_tokens")

    # Table constraints
    __table_args__ = (
        Index("idx_email_verification_tokens_user_id", "user_id"),
        Index("idx_email_verification_tokens_email", "email"),
        Index("idx_email_verification_tokens_token_hash", "token_hash"),
        Index("idx_email_verification_tokens_expires_at", "expires_at"),
        Index("idx_email_verification_tokens_type", "token_type"),
        CheckConstraint(
            "token_type IN ('registration', 'email_change', 'reactivation')",
            name="chk_email_verification_token_type"
        ),
        CheckConstraint(
            "expires_at <= created_at + INTERVAL '7 days'",
            name="chk_email_verification_token_expiry"
        ),
    )

    @property
    def is_expired(self) -> bool:
        """Kiểm tra token có hết hạn không."""
        return datetime.now(timezone.utc) > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Kiểm tra token có hợp lệ để sử dụng không."""
        return not self.is_used and not self.is_revoked and not self.is_expired

# API Models cho EmailVerificationToken
class EmailVerificationRequest(SQLModel):
    """Model yêu cầu xác thực email."""
    token: str = Field(description="Token xác thực email")

class EmailVerificationResponse(SQLModel):
    """Model response sau khi xác thực email."""
    message: str
    email_verified: bool
    account_activated: bool = False
```

## Ví Dụ Sử Dụng SQLModel với FastAPI

### 1. Cấu Hình Database và Engine

```python
# app/core/database.py
"""
Cấu hình database cho SQLModel với PostgreSQL.
"""

from sqlmodel import SQLModel, create_engine, Session
from typing import Generator
import os

# Database URL từ environment variables
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql://skinaid_user:password@localhost:5432/skinaid_db"
)

# Tạo engine với các tối ưu hóa cho PostgreSQL
engine = create_engine(
    DATABASE_URL,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=False  # Set True để debug SQL queries
)

def create_db_and_tables():
    """Tạo tất cả bảng trong database."""
    SQLModel.metadata.create_all(engine)

def get_session() -> Generator[Session, None, None]:
    """
    Dependency function để lấy database session cho FastAPI.

    Sử dụng trong FastAPI endpoints:
    @app.get("/users/")
    def get_users(session: Session = Depends(get_session)):
        return session.exec(select(User)).all()
    """
    with Session(engine) as session:
        yield session
```

### 2. Service Layer với SQLModel

```python
# app/services/auth_service.py
"""
Service layer cho authentication sử dụng SQLModel.
"""

import secrets
import hashlib
from datetime import datetime, timedelta, timezone
from typing import Optional, List
from sqlmodel import Session, select, and_, or_
import bcrypt

from ..models.auth_models import User, Role, UserRole, Session as UserSession
from ..models.auth_models import PasswordResetToken, EmailVerificationToken

class AuthService:
    """Service xử lý authentication với SQLModel."""

    def __init__(self, session: Session):
        self.session = session

    @staticmethod
    def hash_password(password: str) -> str:
        """Mã hóa mật khẩu bằng bcrypt."""
        salt = bcrypt.gensalt(rounds=12)
        password_bytes = password.encode('utf-8')
        hashed = bcrypt.hashpw(password_bytes, salt)
        return hashed.decode('utf-8')

    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """Xác thực mật khẩu."""
        try:
            password_bytes = password.encode('utf-8')
            hashed_bytes = hashed_password.encode('utf-8')
            return bcrypt.checkpw(password_bytes, hashed_bytes)
        except Exception:
            return False

    def create_user(self, user_data: UserCreate) -> User:
        """
        Tạo người dùng mới.

        Args:
            user_data: Dữ liệu người dùng từ UserCreate model

        Returns:
            User object đã tạo

        Raises:
            ValueError: Nếu username hoặc email đã tồn tại
        """
        # Kiểm tra user đã tồn tại
        existing_user = self.session.exec(
            select(User).where(
                or_(User.username == user_data.username, User.email == user_data.email)
            )
        ).first()

        if existing_user:
            if existing_user.username == user_data.username:
                raise ValueError("Tên đăng nhập đã tồn tại")
            else:
                raise ValueError("Email đã tồn tại")

        # Tạo user mới
        user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=self.hash_password(user_data.password),
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            phone_number=user_data.phone_number,
            metadata=user_data.metadata,
            account_status="pending"
        )

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)

        return user

    def authenticate_user(self, username_or_email: str, password: str) -> Optional[User]:
        """
        Xác thực người dùng.

        Args:
            username_or_email: Tên đăng nhập hoặc email
            password: Mật khẩu

        Returns:
            User object nếu xác thực thành công, None nếu thất bại
        """
        # Tìm user theo username hoặc email
        user = self.session.exec(
            select(User).where(
                or_(User.username == username_or_email, User.email == username_or_email)
            )
        ).first()

        if not user:
            return None

        # Kiểm tra tài khoản có bị khóa không
        if user.is_locked:
            return None

        # Xác thực mật khẩu
        if not self.verify_password(password, user.password_hash):
            # Tăng số lần đăng nhập thất bại
            user.failed_login_attempts += 1

            # Khóa tài khoản sau 5 lần thất bại
            if user.failed_login_attempts >= 5:
                user.locked_until = datetime.now(timezone.utc) + timedelta(minutes=30)

            self.session.commit()
            return None

        # Đăng nhập thành công - reset failed attempts
        user.failed_login_attempts = 0
        user.locked_until = None
        user.last_login = datetime.now(timezone.utc)
        self.session.commit()

        return user

    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Lấy user theo ID."""
        return self.session.get(User, user_id)

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Lấy user theo email."""
        return self.session.exec(
            select(User).where(User.email == email)
        ).first()

    def assign_role_to_user(
        self,
        user_id: str,
        role_name: str,
        assigned_by_id: Optional[str] = None
    ) -> UserRole:
        """
        Phân quyền cho người dùng.

        Args:
            user_id: ID người dùng
            role_name: Tên vai trò
            assigned_by_id: ID người phân quyền

        Returns:
            UserRole object

        Raises:
            ValueError: Nếu user hoặc role không tồn tại, hoặc đã được phân quyền
        """
        # Kiểm tra user tồn tại
        user = self.get_user_by_id(user_id)
        if not user:
            raise ValueError("Người dùng không tồn tại")

        # Kiểm tra role tồn tại
        role = self.session.exec(
            select(Role).where(Role.name == role_name)
        ).first()
        if not role:
            raise ValueError("Vai trò không tồn tại")

        # Kiểm tra đã được phân quyền chưa
        existing_assignment = self.session.exec(
            select(UserRole).where(
                and_(
                    UserRole.user_id == user_id,
                    UserRole.role_id == role.id,
                    UserRole.is_active == True
                )
            )
        ).first()

        if existing_assignment:
            raise ValueError("Người dùng đã có vai trò này")

        # Tạo phân quyền mới
        user_role = UserRole(
            user_id=user_id,
            role_id=role.id,
            assigned_by=assigned_by_id
        )

        self.session.add(user_role)
        self.session.commit()
        self.session.refresh(user_role)

        return user_role

    def get_user_permissions(self, user_id: str) -> List[str]:
        """
        Lấy tất cả quyền hạn của người dùng dựa trên vai trò.

        Args:
            user_id: ID người dùng

        Returns:
            Danh sách quyền hạn
        """
        # Lấy tất cả vai trò active của user
        user_roles = self.session.exec(
            select(UserRole)
            .join(Role)
            .where(
                and_(
                    UserRole.user_id == user_id,
                    UserRole.is_active == True,
                    Role.is_active == True,
                    or_(
                        UserRole.expires_at.is_(None),
                        UserRole.expires_at > datetime.now(timezone.utc)
                    )
                )
            )
        ).all()

        # Thu thập tất cả quyền hạn
        permissions = set()
        for user_role in user_roles:
            permissions.update(user_role.role.permissions)

        return list(permissions)
```

### 3. FastAPI Endpoints với SQLModel

```python
# app/api/v1/auth.py
"""
Authentication endpoints sử dụng SQLModel.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlmodel import Session
from typing import List

from ...core.database import get_session
from ...services.auth_service import AuthService
from ...models.auth_models import (
    User, UserCreate, UserRead, UserLogin, UserUpdate,
    TokenResponse, PasswordResetRequest, PasswordResetConfirm
)

router = APIRouter(prefix="/auth", tags=["authentication"])

def get_auth_service(session: Session = Depends(get_session)) -> AuthService:
    """Dependency để lấy AuthService."""
    return AuthService(session)

@router.post("/register", response_model=dict, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Đăng ký người dùng mới.

    Tạo tài khoản mới với thông tin được cung cấp.
    Tài khoản sẽ ở trạng thái 'pending' cho đến khi xác thực email.
    """
    try:
        user = auth_service.create_user(user_data)

        # Phân quyền mặc định 'patient' cho user mới
        auth_service.assign_role_to_user(user.id, "patient")

        return {
            "message": "Đăng ký thành công",
            "user_id": user.id,
            "email_verification_required": True
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/login", response_model=TokenResponse)
async def login_user(
    login_data: UserLogin,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service),
    session: Session = Depends(get_session)
):
    """
    Đăng nhập và trả về access tokens.

    Xác thực thông tin đăng nhập và trả về JWT access token và refresh token.
    """
    # Xác thực người dùng
    user = auth_service.authenticate_user(
        login_data.username_or_email,
        login_data.password
    )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Tên đăng nhập/email hoặc mật khẩu không đúng",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Kiểm tra trạng thái tài khoản
    if user.account_status != "active":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Tài khoản đang ở trạng thái {user.account_status}. Vui lòng liên hệ hỗ trợ."
        )

    # Tạo session và tokens (implementation tùy thuộc vào SessionService)
    # Ở đây chỉ là ví dụ response
    return TokenResponse(
        access_token="example_jwt_token",
        refresh_token="example_refresh_token",
        token_type="bearer",
        expires_in=1800  # 30 minutes
    )

@router.get("/profile", response_model=UserRead)
async def get_user_profile(
    current_user: User = Depends(get_current_user)  # Dependency sẽ được implement
):
    """
    Lấy thông tin profile của người dùng hiện tại.

    Trả về thông tin chi tiết của người dùng đã xác thực.
    """
    return UserRead(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        phone_number=current_user.phone_number,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        account_status=current_user.account_status,
        metadata=current_user.metadata,
        roles=current_user.get_roles(),
        created_at=current_user.created_at,
        last_login=current_user.last_login
    )

@router.put("/profile", response_model=UserRead)
async def update_user_profile(
    profile_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Cập nhật thông tin profile của người dùng.

    Cho phép người dùng cập nhật thông tin cá nhân.
    """
    # Cập nhật các trường được cung cấp
    if profile_data.first_name is not None:
        current_user.first_name = profile_data.first_name
    if profile_data.last_name is not None:
        current_user.last_name = profile_data.last_name
    if profile_data.phone_number is not None:
        current_user.phone_number = profile_data.phone_number
    if profile_data.metadata is not None:
        current_user.metadata.update(profile_data.metadata)

    session.add(current_user)
    session.commit()
    session.refresh(current_user)

    return UserRead(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        phone_number=current_user.phone_number,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        account_status=current_user.account_status,
        metadata=current_user.metadata,
        roles=current_user.get_roles(),
        created_at=current_user.created_at,
        last_login=current_user.last_login
    )

@router.get("/users", response_model=List[UserRead])
async def get_all_users(
    current_user: User = Depends(require_permission("manage_users")),  # Admin only
    session: Session = Depends(get_session)
):
    """
    Lấy danh sách tất cả người dùng (chỉ admin).

    Trả về danh sách tất cả người dùng trong hệ thống.
    Yêu cầu quyền 'manage_users'.
    """
    users = session.exec(select(User)).all()
    return [
        UserRead(
            id=user.id,
            username=user.username,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            phone_number=user.phone_number,
            is_active=user.is_active,
            is_verified=user.is_verified,
            account_status=user.account_status,
            metadata=user.metadata,
            roles=user.get_roles(),
            created_at=user.created_at,
            last_login=user.last_login
        )
        for user in users
    ]
```

## Ưu Điểm của SQLModel trong Dự Án SkinAid

### 1. **Type Safety và IDE Support**
- Hỗ trợ type hints đầy đủ
- Autocomplete và error detection trong IDE
- Giảm lỗi runtime do type mismatch

### 2. **Code Reuse**
- Một model definition cho cả database và API
- Giảm duplicate code
- Dễ dàng maintain và update

### 3. **Validation Tự Động**
- Pydantic validation cho tất cả input/output
- Custom validators cho business logic
- Error messages rõ ràng và có thể customize

### 4. **FastAPI Integration**
- Automatic OpenAPI documentation
- Request/response serialization
- Dependency injection support

### 5. **SQLAlchemy Power**
- Tất cả tính năng của SQLAlchemy ORM
- Complex queries và relationships
- Database migrations với Alembic

## Best Practices cho SQLModel

### 1. **Model Organization**
```python
# Tách base models và table models
class UserBase(SQLModel):
    # Shared fields
    pass

class User(UserBase, table=True):
    # Database-specific fields
    pass

class UserCreate(UserBase):
    # API creation fields
    pass

class UserRead(UserBase):
    # API response fields
    pass
```

### 2. **Validation**
```python
# Sử dụng Pydantic validators
@validator('email')
def validate_email(cls, v):
    # Custom email validation
    return v

# Field constraints
username: str = Field(regex=r'^[A-Za-z0-9_]+$')
```

### 3. **Relationships**
```python
# Lazy loading cho performance
user_roles: List["UserRole"] = Relationship(
    back_populates="user",
    sa_relationship_kwargs={"lazy": "select"}
)
```

---

*Tài liệu SQLModel hoàn chỉnh cho hệ thống xác thực SkinAid, cung cấp foundation mạnh mẽ cho FastAPI + PostgreSQL application.*
